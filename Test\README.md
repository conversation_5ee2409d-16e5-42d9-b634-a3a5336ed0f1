# Mail_Auto Test Suite

This folder contains test scripts for the Mail_Auto email automation system.

## Running Tests

### Run All Tests
```bash
cd Test
python run_all_tests.py
```

### Run Individual Tests
```bash
cd Test
python test_new_features.py
python test_swedish_language.py
```

## Test Files

### `test_new_features.py`
Tests the core new features implemented:
- ✅ Enhanced subfolder format with `{company_name}` and `{document_year}` placeholders
- ✅ Structured ChatGPT summaries with detailed information extraction
- ✅ Preferred language support for multilingual summaries
- ✅ External email filtering to process only external emails

### `test_swedish_language.py`
Specifically tests the Swedish language functionality:
- ✅ Verifies Swedish language is correctly extracted from tenant config
- ✅ Confirms Swedish language instruction is added to ChatGPT prompts
- ✅ Tests the complete language flow from config to API call

### `test_dynamic_recipient_names.py`
Tests the dynamic recipient name functionality:
- ✅ Verifies first name extraction from full names (e.g., "<PERSON>" → "<PERSON>")
- ✅ Confirms `{recipient_name}` placeholder is present in all email templates
- ✅ Tests notification settings merge and recipient processing
- ✅ Validates template formatting with dynamic names

### `run_all_tests.py`
Test runner that executes all test files and provides a summary report.

## Test Results

All tests should pass with the current configuration:

```
🎯 Results: 3/3 tests passed
🎉 All tests passed!
```

## Configuration Testing

The tests verify that your `tenants/prototype/config.json` has:

1. **Swedish Language**: `"preferred_language": "Swedish"` in the `defaults` section
2. **External Email Filtering**: Properly configured with company domains
3. **Enhanced Subfolder Format**: Using `{document_year}` and `{company_name}` placeholders
4. **Document Type Configurations**: All document types properly configured

## Troubleshooting

If tests fail:

1. **Language Issues**: Ensure `"preferred_language": "Swedish"` is in the `defaults` section, not individual document types
2. **Import Errors**: Make sure you're running tests from within the `Test` folder
3. **Config Issues**: Verify your `tenants/prototype/config.json` file is valid JSON

## Adding New Tests

To add new test files:

1. Create a new Python file in the `Test` folder
2. Add the filename to the `test_files` list in `run_all_tests.py`
3. Follow the existing test pattern with descriptive print statements

Example test structure:
```python
#!/usr/bin/env python3
import sys
import os

# Add parent directory to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_my_feature():
    print("🧪 Testing my feature...")
    # Your test code here
    print("✅ Test passed!")

if __name__ == "__main__":
    test_my_feature()
```
