"""Email notification helper using Microsoft Graph API.

This module sends an email (optionally with the processed PDF attached) when
``notification.enabled`` is ``true`` for a document type in ``tenant_config``.

Public API
~~~~~~~~~~
    send_notification(doc_type, tenant_config, summary, pdf_bytes, filename, headers)
"""
from __future__ import annotations

import base64
import json
from typing import Dict, Any, List

import requests

__all__ = ["send_notification"]

_GRAPH_ENDPOINT = "https://graph.microsoft.com/v1.0/me/sendMail"


def _merge_notification_settings(tenant_config: Dict[str, Any], doc_type: str) -> Dict[str, Any]:
    """Return effective notification settings for *doc_type*.

    Precedence: per-type override → tenant-wide defaults → hard-coded fallback.
    """
    defaults: Dict[str, Any] = tenant_config.get("defaults", {}).get("notification", {})
    overrides: Dict[str, Any] = (
        tenant_config.get("document_types", {}).get(doc_type, {}).get("notification", {})
    )

    merged = {**defaults, **overrides}
    
    # Check if notification is enabled via actions.notify
    default_actions = tenant_config.get("defaults", {}).get("actions", {})
    doc_actions = tenant_config.get("document_types", {}).get(doc_type, {}).get("actions", {})
    should_notify = doc_actions.get("notify", default_actions.get("notify", False))
    
    # Ensure critical keys exist
    merged.setdefault("enabled", should_notify)
    merged.setdefault("recipients", [])
    # Don't set a default email_template here - let send_notification handle it
    # This allows the system to use localized templates when no custom template is provided
    return merged


def _extract_first_name(full_name: str) -> str:
    """Extract first name from a full name string."""
    if not full_name:
        return "there"  # Friendly fallback

    # Split by space and take first part
    parts = full_name.strip().split()
    if parts:
        return parts[0]
    return "there"


def _build_message(
    subject: str,
    body_text: str,
    recipients: List[Dict[str, str]],
    pdf_bytes: bytes | None,
    filename: str | None,
) -> Dict[str, Any]:
    """Build the JSON payload for Graph ``/sendMail``."""
    msg: Dict[str, Any] = {
        "subject": subject,
        "body": {"contentType": "Text", "content": body_text},
        "toRecipients": [
            {
                "emailAddress": {"name": r.get("name", ""), "address": r["email"]}
            }
            for r in recipients
        ],
    }

    if pdf_bytes and filename:
        attachment = {
            "@odata.type": "#microsoft.graph.fileAttachment",
            "name": filename,
            "contentType": "application/pdf",
            "contentBytes": base64.b64encode(pdf_bytes).decode(),
        }
        msg.setdefault("attachments", []).append(attachment)

    return {"message": msg, "saveToSentItems": "false"}


def _get_localized_subject(doc_type: str, language: str) -> str:
    """Get localized email subject based on language."""

    # Subject translations
    subjects = {
        "English": f"New {doc_type} received",
        "Swedish": f"Nytt {doc_type} mottaget",
        "German": f"Neues {doc_type} erhalten",
        "French": f"Nouveau {doc_type} reçu",
        "Spanish": f"Nuevo {doc_type} recibido",
        "Italian": f"Nuovo {doc_type} ricevuto",
        "Dutch": f"Nieuw {doc_type} ontvangen",
        "Norwegian": f"Nytt {doc_type} mottatt",
        "Danish": f"Nyt {doc_type} modtaget",
        "Finnish": f"Uusi {doc_type} vastaanotettu"
    }

    return subjects.get(language, f"New {doc_type} received")


def _get_localized_template_fallback(language: str) -> str:
    """Get localized fallback template based on language."""

    templates = {
        "English": "Hi {recipient_name},\n\nA new {doc_type} has arrived and was auto-filed.\n\n{summary}\n\nPlease review the attachment.",
        "Swedish": "Hej {recipient_name},\n\nEtt nytt {doc_type} har anlänt och arkiverats automatiskt.\n\n{summary}\n\nVänligen granska bilagan.",
        "German": "Hallo {recipient_name},\n\nEin neues {doc_type} ist angekommen und wurde automatisch abgelegt.\n\n{summary}\n\nBitte überprüfen Sie den Anhang.",
        "French": "Bonjour {recipient_name},\n\nUn nouveau {doc_type} est arrivé et a été classé automatiquement.\n\n{summary}\n\nVeuillez examiner la pièce jointe.",
        "Spanish": "Hola {recipient_name},\n\nUn nuevo {doc_type} ha llegado y se ha archivado automáticamente.\n\n{summary}\n\nPor favor, revise el archivo adjunto.",
        "Italian": "Ciao {recipient_name},\n\nUn nuovo {doc_type} è arrivato ed è stato archiviato automaticamente.\n\n{summary}\n\nSi prega di rivedere l'allegato.",
        "Dutch": "Hallo {recipient_name},\n\nEen nieuw {doc_type} is aangekomen en automatisch gearchiveerd.\n\n{summary}\n\nControleer de bijlage.",
        "Norwegian": "Hei {recipient_name},\n\nEt nytt {doc_type} har ankommet og blitt automatisk arkivert.\n\n{summary}\n\nVennligst gjennomgå vedlegget.",
        "Danish": "Hej {recipient_name},\n\nEt nyt {doc_type} er ankommet og blevet automatisk arkiveret.\n\n{summary}\n\nGennemgå venligst vedhæftningen.",
        "Finnish": "Hei {recipient_name},\n\nUusi {doc_type} on saapunut ja arkistoitu automaattisesti.\n\n{summary}\n\nTarkista liite."
    }

    return templates.get(language, templates["English"])


def send_notification(
    doc_type: str,
    tenant_config: Dict[str, Any],
    summary: str,
    pdf_bytes: bytes,
    filename: str,
    headers: Dict[str, str],
) -> None:
    """Send an email notification for *doc_type* if enabled in *tenant_config*.

    The function is *idempotent*: if notifications are disabled it just returns.
    """
    settings = _merge_notification_settings(tenant_config, doc_type)
    if not settings.get("enabled", False):
        return  # Notification disabled → silent no-op

    recipients = settings.get("recipients", [])
    if not recipients:
        print("⚠️  Notification enabled but no recipients configured – skipping email")
        return

    # Get preferred language from tenant config
    preferred_language = tenant_config.get("defaults", {}).get("preferred_language", "English")

    # Get template (use configured template or localized fallback)
    template = settings.get("email_template")
    if not template:
        template = _get_localized_template_fallback(preferred_language)

    # Get localized subject
    subject = _get_localized_subject(doc_type, preferred_language)

    # Send individual emails to each recipient with personalized content
    for recipient in recipients:
        recipient_name = _extract_first_name(recipient.get("name", ""))

        # Format template with recipient-specific information
        personalized_body = template.format(
            doc_type=doc_type,
            summary=summary or "(no summary)",
            recipient_name=recipient_name
        )

        # Send to single recipient
        single_recipient = [recipient]
        payload = _build_message(subject, personalized_body, single_recipient, pdf_bytes, filename)

        resp = requests.post(_GRAPH_ENDPOINT, headers=headers, json=payload)
        if resp.status_code in (202, 200):
            print(f"📧 Notification email sent to {recipient.get('name', recipient['email'])}")
        else:
            try:
                err = resp.json()
            except ValueError:
                err = resp.text
            print(f"❌ Failed to send notification to {recipient.get('name', recipient['email'])}: {resp.status_code} {err}")


