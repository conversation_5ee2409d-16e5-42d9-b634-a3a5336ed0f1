#!/usr/bin/env python3
"""
Test runner for all Mail_Auto tests.
Run this script to execute all available tests.
"""

import sys
import os
import subprocess

def run_test(test_file):
    """Run a single test file and return success status."""
    print(f"\n{'='*60}")
    print(f"🧪 Running {test_file}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run([sys.executable, test_file], 
                              capture_output=False, 
                              text=True, 
                              cwd=os.path.dirname(os.path.abspath(__file__)))
        
        if result.returncode == 0:
            print(f"✅ {test_file} completed successfully")
            return True
        else:
            print(f"❌ {test_file} failed with return code {result.returncode}")
            return False
            
    except Exception as e:
        print(f"❌ Error running {test_file}: {e}")
        return False

def main():
    """Run all tests in the Test directory."""
    print("🚀 Mail_Auto Test Suite")
    print("=" * 60)
    
    # List of test files to run
    test_files = [
        "test_new_features.py",
        "test_swedish_language.py",
        "test_dynamic_recipient_names.py",
        "test_language_aware_notifications.py"
    ]
    
    results = {}
    
    for test_file in test_files:
        if os.path.exists(test_file):
            results[test_file] = run_test(test_file)
        else:
            print(f"⚠️  Test file {test_file} not found")
            results[test_file] = False
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 Test Summary")
    print(f"{'='*60}")
    
    passed = sum(1 for success in results.values() if success)
    total = len(results)
    
    for test_file, success in results.items():
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_file}")
    
    print(f"\n🎯 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed!")
        return 0
    else:
        print("⚠️  Some tests failed")
        return 1

if __name__ == "__main__":
    sys.exit(main())
